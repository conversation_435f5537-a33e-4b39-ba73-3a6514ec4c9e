'use client';

import { motion } from 'framer-motion';
import GlassCard from '@/components/ui/GlassCard';
import { INTEGRATIONS } from '@/lib/constants';
import { staggerContainer, staggerItem } from '@/lib/animations';

const integrationCategories = [
  {
    name: 'Communication',
    integrations: [
      { name: 'Slack', icon: '💬', description: 'Team messaging and notifications' },
      { name: 'Microsoft Teams', icon: '🟣', description: 'Enterprise collaboration' },
      { name: 'Discord', icon: '🎮', description: 'Community and team chat' },
    ]
  },
  {
    name: 'Meetings',
    integrations: [
      { name: 'Zoom', icon: '🔵', description: 'Video conferencing' },
      { name: 'Google Meet', icon: '🟢', description: 'Google Workspace meetings' },
      { name: 'WebEx', icon: '🔴', description: 'Enterprise video meetings' },
    ]
  },
  {
    name: 'Project Management',
    integrations: [
      { name: 'Jira', icon: '🔷', description: 'Issue tracking and project management' },
      { name: 'Linear', icon: '⚡', description: 'Modern issue tracking' },
      { name: 'Asana', icon: '🔺', description: 'Team project management' },
    ]
  },
  {
    name: 'Development',
    integrations: [
      { name: 'GitHub', icon: '🐙', description: 'Code repository and CI/CD' },
      { name: 'GitLab', icon: '🦊', description: 'DevOps platform' },
      { name: 'Bitbucket', icon: '🪣', description: 'Atlassian Git solution' },
    ]
  },
];

export default function IntegrationsPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 gradient-secondary">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Integrations
          </motion.h1>
          <motion.p
            className="text-xl text-white/80"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Connect Flow with all your favorite tools and services for seamless workflow automation.
          </motion.p>
        </div>
      </section>

      {/* Integrations by Category */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {integrationCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.name}
              className="mb-16 last:mb-0"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
            >
              <h2 className="text-2xl font-bold text-white mb-8 text-center">
                {category.name}
              </h2>
              
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                variants={staggerContainer}
                initial="initial"
                animate="animate"
              >
                {category.integrations.map((integration, index) => (
                  <motion.div key={integration.name} variants={staggerItem}>
                    <GlassCard className="p-6 text-center hover:scale-105 transition-transform">
                      <div className="text-4xl mb-4">{integration.icon}</div>
                      <h3 className="text-white font-semibold text-lg mb-2">
                        {integration.name}
                      </h3>
                      <p className="text-white/70 text-sm">
                        {integration.description}
                      </p>
                    </GlassCard>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              More Integrations Coming Soon
            </h2>
            <p className="text-white/70 text-lg mb-8">
              We're constantly adding new integrations. Don't see your favorite tool? Let us know!
            </p>
            <button className="bg-primary hover:bg-primary-end text-white px-6 py-3 rounded-lg font-medium transition-colors">
              Request Integration
            </button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}