'use client';

import { motion } from 'framer-motion';
import GlassCard from '@/components/ui/GlassCard';
import { staggerContainer, staggerItem } from '@/lib/animations';

export default function AboutPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 gradient-dark">
        <div className="max-w-4xl mx-auto text-center">
          <motion.h1
            className="text-4xl sm:text-5xl font-bold text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            About Flow
          </motion.h1>
          <motion.p
            className="text-xl text-white/80"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            We're building the future of AI-powered workplace automation to help teams work smarter, not harder.
          </motion.p>
        </div>
      </section>

      {/* Mission */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-6">Our Mission</h2>
            <p className="text-white/70 text-lg leading-relaxed">
              At Flow, we believe that artificial intelligence should enhance human creativity and productivity, 
              not replace it. Our mission is to eliminate the busy work that keeps teams from focusing on 
              what truly matters - innovation, collaboration, and meaningful impact.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {[
              {
                icon: '🎯',
                title: 'Purpose-Driven',
                description: 'Every feature we build serves a clear purpose: to make your work more meaningful and productive.'
              },
              {
                icon: '🔒',
                title: 'Privacy-First',
                description: 'Your data belongs to you. We build with privacy and security as fundamental principles, not afterthoughts.'
              },
              {
                icon: '🌍',
                title: 'Global Impact',
                description: 'We\'re building for teams worldwide, with support for multiple languages, timezones, and work cultures.'
              }
            ].map((value, index) => (
              <motion.div key={index} variants={staggerItem}>
                <GlassCard className="p-6 text-center">
                  <div className="text-3xl mb-4">{value.icon}</div>
                  <h3 className="text-white font-semibold text-lg mb-3">{value.title}</h3>
                  <p className="text-white/70">{value.description}</p>
                </GlassCard>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Story */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-8">Our Story</h2>
            <div className="text-white/70 text-lg leading-relaxed space-y-6">
              <p>
                Flow was born from a simple observation: despite having more tools than ever, 
                teams were spending more time managing their tools than actually working. 
                Meetings went unrecorded, action items were lost, and valuable insights 
                disappeared into the void of busy calendars.
              </p>
              <p>
                We set out to build something different - an AI-powered platform that could 
                intelligently connect the dots between meetings, tasks, and knowledge, 
                turning conversations into action and ideas into results.
              </p>
              <p>
                Today, Flow helps thousands of teams worldwide transform their workflows, 
                capture institutional knowledge, and focus on what they do best: creating 
                amazing products and services.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Transform Your Workflow?
            </h2>
            <p className="text-white/70 text-lg mb-8">
              Join thousands of teams already using Flow to work smarter and achieve more.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-primary hover:bg-primary-end text-white px-8 py-3 rounded-lg font-medium transition-colors">
                Start Free Trial
              </button>
              <button className="border border-white/30 hover:bg-white/10 text-white px-8 py-3 rounded-lg font-medium transition-colors">
                Schedule Demo
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}