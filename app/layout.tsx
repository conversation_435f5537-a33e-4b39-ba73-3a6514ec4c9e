import type { Metadata } from "next";
import "./globals.css";
import Navigation from "@/components/common/Navigation";
import FABGroup from "@/components/ui/FAB";

export const metadata: Metadata = {
  title: "Flow - AI-Powered Workflow Automation Platform",
  description: "Transform how teams collaborate by intelligently connecting meetings, tasks, and knowledge across your entire workflow. Let AI handle the busy work while you focus on what matters.",
  keywords: ["AI", "workflow automation", "meetings", "productivity", "collaboration"],
  authors: [{ name: "Flow Platform" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#667eea",
  openGraph: {
    title: "Flow - AI-Powered Workflow Automation Platform",
    description: "Transform how teams collaborate with AI-powered workflow automation",
    url: "https://flow.example.com",
    siteName: "Flow",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Flow - AI Workflow Automation",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Flow - AI-Powered Workflow Automation Platform",
    description: "Transform how teams collaborate with AI-powered workflow automation",
    images: ["/og-image.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="antialiased gradient-primary min-h-screen">
        {/* Navigation */}
        <Navigation />
        
        {/* Main content */}
        <main className="relative z-10">
          {children}
        </main>
        
        {/* Floating action buttons */}
        <FABGroup />
      </body>
    </html>
  );
}
