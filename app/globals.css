@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --primary-gradient-start: #667eea;
  --primary-gradient-end: #764ba2;
  
  /* Secondary Colors */
  --secondary-gradient-start: #10b981;
  --secondary-gradient-end: #059669;
  
  /* Dark Gradient */
  --dark-gradient-start: #1e293b;
  --dark-gradient-end: #334155;
  
  /* Base Colors */
  --background: #f8fafc;
  --foreground: #1a202c;
  --text-secondary: #64748b;
  
  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-blur: 20px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --text-secondary: #94a3b8;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', sans-serif;
  min-height: 100vh;
  overflow-x: hidden;
}

@layer base {
  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

@layer components {
  /* Glass Morphism */
  .glass {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    border: 1px solid var(--glass-border);
  }
  
  /* Primary Gradient */
  .gradient-primary {
    background: linear-gradient(135deg, var(--primary-gradient-start) 0%, var(--primary-gradient-end) 100%);
  }
  
  /* Secondary Gradient */
  .gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-gradient-start) 0%, var(--secondary-gradient-end) 100%);
  }
  
  /* Dark Gradient */
  .gradient-dark {
    background: linear-gradient(135deg, var(--dark-gradient-start) 0%, var(--dark-gradient-end) 100%);
  }
  
  /* Gradient Text */
  .gradient-text {
    background: linear-gradient(45deg, #ffffff, #f0f0f0, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: textGlow 3s ease-in-out infinite;
  }
  
  /* Hover Lift Effect */
  .hover-lift {
    transition: all 0.3s ease;
  }
  
  .hover-lift:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Animation Classes */
  .animate-float {
    animation: float 20s infinite linear;
  }
  
  .animate-logo-spin {
    animation: logoSpin 20s linear infinite;
  }
  
  /* Custom button shadows */
  .shadow-3xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  }

  /* Hide any leftover sparkle effects */
  .fixed.pointer-events-none.rounded-full.bg-white,
  .fixed.pointer-events-none.z-50.rounded-full.bg-white,
  div[style*="position: fixed"][style*="pointer-events: none"][style*="border-radius"][style*="background"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    transform: translateY(-100px) rotate(180deg);
    opacity: 0.2;
  }
  100% {
    transform: translateY(0px) rotate(360deg);
    opacity: 0.1;
  }
}

@keyframes logoSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
