'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { FeatureHero } from '@/components/sections/Hero';
import { FeatureCard } from '@/components/ui/GlassCard';
import GlassCard from '@/components/ui/GlassCard';
import Button from '@/components/common/Button';
import { staggerContainer, staggerItem } from '@/lib/animations';

const scheduleFeatures = [
  {
    icon: '🤖',
    title: 'AI Meeting Scheduling',
    description: 'Cross-timezone optimization with preference learning and smart suggestions.',
    benefits: ['Timezone intelligence', 'Preference learning', 'Conflict resolution'],
  },
  {
    icon: '🔄',
    title: 'Recurring Job Automation',
    description: 'Flexible cron expressions with holiday awareness and automatic retries.',
    benefits: ['Flexible scheduling', 'Holiday awareness', 'Auto-retry logic'],
  },
  {
    icon: '⚡',
    title: 'Run Jobs Now',
    description: 'Instant execution with parameter override and real-time monitoring.',
    benefits: ['Instant execution', 'Parameter override', 'Real-time monitoring'],
  },
  {
    icon: '📊',
    title: 'Schedule Analytics',
    description: 'Meeting effectiveness insights and time optimization recommendations.',
    benefits: ['Effectiveness metrics', 'Time optimization', 'Productivity insights'],
  },
  {
    icon: '🌍',
    title: 'Global Time Management',
    description: 'Timezone intelligence with work hours respect across global teams.',
    benefits: ['Global timezone support', 'Work hours respect', 'Cultural awareness'],
  },
  {
    icon: '🔗',
    title: 'Calendar Integration',
    description: 'Seamless sync with Google Calendar, Outlook, Calendly, and more.',
    benefits: ['Multi-platform sync', 'Real-time updates', 'Conflict detection'],
  },
];

const jobTypes = [
  {
    id: 'recurring',
    name: 'Recurring Jobs',
    examples: ['Weekly Reports', 'Data Sync', 'Digest Emails'],
  },
  {
    id: 'one-time',
    name: 'One-time Tasks',
    examples: ['Campaign Launch', 'System Backup', 'Data Migration'],
  },
  {
    id: 'event-driven',
    name: 'Event-driven',
    examples: ['New User Welcome', 'Error Alerts', 'Deploy Notifications'],
  },
  {
    id: 'conditional',
    name: 'Conditional',
    examples: ['Cleanup Tasks', 'Status Updates', 'Monitoring Checks'],
  },
];

// Mock calendar component
function CalendarDemo() {
  const [currentDate] = useState(new Date(2024, 11, 1)); // December 2024
  const daysInMonth = new Date(2024, 11 + 1, 0).getDate();
  const firstDayOfMonth = new Date(2024, 11, 1).getDay();
  
  const events = [
    { day: 5, title: 'Team Standup', type: 'recurring' },
    { day: 10, title: 'Product Review', type: 'meeting' },
    { day: 15, title: 'Sprint Planning', type: 'meeting' },
    { day: 20, title: 'Demo Day', type: 'event' },
    { day: 25, title: 'Holiday Break', type: 'holiday' },
  ];

  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  const emptyDays = Array.from({ length: firstDayOfMonth }, (_, i) => i);

  return (
    <motion.div
      className="bg-gray-900/80 rounded-lg p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-white font-semibold text-lg">December 2024</h3>
        <div className="flex gap-2">
          <button className="text-white/60 hover:text-white">‹</button>
          <button className="text-white/60 hover:text-white">›</button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-2 mb-4">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-white/60 text-sm py-2 font-medium">
            {day}
          </div>
        ))}
        
        {/* Empty days */}
        {emptyDays.map((_, index) => (
          <div key={`empty-${index}`} className="h-10" />
        ))}
        
        {/* Calendar days */}
        {days.map(day => {
          const dayEvents = events.filter(event => event.day === day);
          const isToday = day === 15; // Mock today
          
          return (
            <motion.div
              key={day}
              className={`relative h-10 flex items-center justify-center text-sm rounded cursor-pointer transition-colors ${
                isToday 
                  ? 'bg-primary text-white font-semibold' 
                  : 'text-white/70 hover:bg-white/10'
              }`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: day * 0.01 }}
              whileHover={{ scale: 1.1 }}
            >
              {day}
              {dayEvents.map((event, index) => (
                <div
                  key={index}
                  className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full ${
                    event.type === 'recurring' ? 'bg-green-400' :
                    event.type === 'meeting' ? 'bg-blue-400' :
                    event.type === 'event' ? 'bg-yellow-400' :
                    'bg-red-400'
                  }`}
                />
              ))}
            </motion.div>
          );
        })}
      </div>

      {/* Event legend */}
      <div className="flex flex-wrap gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-400 rounded-full" />
          <span className="text-white/60">Recurring</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-blue-400 rounded-full" />
          <span className="text-white/60">Meetings</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-yellow-400 rounded-full" />
          <span className="text-white/60">Events</span>
        </div>
      </div>
    </motion.div>
  );
}

// Mock AI scheduling component
function AISchedulingDemo() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const timeSlots = [
    { time: '2:00 PM PST', gmt: '10:00 PM GMT', score: 95, recommended: true },
    { time: '3:00 PM PST', gmt: '11:00 PM GMT', score: 88, recommended: true },
    { time: '4:00 PM PST', gmt: '12:00 AM GMT', score: 72, recommended: false },
    { time: '10:00 AM PST', gmt: '6:00 PM GMT', score: 65, recommended: false },
  ];

  const handleAnalyze = () => {
    setIsAnalyzing(true);
    setShowResults(false);
    
    setTimeout(() => {
      setIsAnalyzing(false);
      setShowResults(true);
    }, 2000);
  };

  return (
    <motion.div
      className="bg-gray-900/80 rounded-lg p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <h3 className="text-white font-semibold text-lg mb-4">AI Meeting Scheduler</h3>
      
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-white/70 text-sm mb-2">Meeting Duration</label>
          <select className="w-full bg-gray-800 text-white rounded px-3 py-2 text-sm">
            <option>30 minutes</option>
            <option>1 hour</option>
            <option>1.5 hours</option>
          </select>
        </div>
        
        <div>
          <label className="block text-white/70 text-sm mb-2">Participants</label>
          <div className="flex gap-2">
            <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">Sarah (PST)</span>
            <span className="bg-green-600 text-white px-2 py-1 rounded text-xs">Mike (GMT)</span>
            <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs">Lisa (EST)</span>
          </div>
        </div>
      </div>

      <button
        onClick={handleAnalyze}
        disabled={isAnalyzing}
        className="w-full bg-primary hover:bg-primary-end text-white py-2 rounded font-medium transition-colors disabled:opacity-50"
      >
        {isAnalyzing ? (
          <span className="flex items-center justify-center gap-2">
            <motion.div
              className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            AI Analyzing...
          </span>
        ) : (
          '✨ Find Perfect Times'
        )}
      </button>

      {showResults && (
        <motion.div
          className="mt-6 space-y-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-green-400 text-sm font-medium mb-3">
            ✨ Perfect Times Found
          </div>
          
          {timeSlots.map((slot, index) => (
            <motion.div
              key={index}
              className={`p-3 rounded border transition-colors cursor-pointer ${
                slot.recommended 
                  ? 'border-green-400 bg-green-400/10 hover:bg-green-400/20' 
                  : 'border-gray-600 bg-gray-800/50 hover:bg-gray-700/50'
              }`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-white font-medium">{slot.time}</div>
                  <div className="text-white/60 text-xs">{slot.gmt}</div>
                </div>
                <div className="flex items-center gap-2">
                  {slot.recommended && (
                    <span className="bg-green-400 text-black text-xs px-2 py-1 rounded font-medium">
                      AI Pick
                    </span>
                  )}
                  <span className="text-white/60 text-sm">{slot.score}%</span>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      )}
    </motion.div>
  );
}

export default function SchedulesPage() {
  const [activeTab, setActiveTab] = useState('recurring');

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <FeatureHero
        icon="📅"
        title="Smart Scheduling"
        subtitle="Let AI handle your calendar complexity. Automated scheduling, recurring jobs, and intelligent time optimization for maximum productivity."
        gradient="dark"
      />

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Intelligent Scheduling & Automation
            </h2>
            <p className="text-white/70 text-lg max-w-2xl mx-auto">
              From AI-powered meeting scheduling to complex recurring job automation, Flow handles it all.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {scheduleFeatures.map((feature, index) => (
              <motion.div key={index} variants={staggerItem}>
                <FeatureCard
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  benefits={feature.benefits}
                  gradient="dark"
                />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              See Smart Scheduling in Action
            </h2>
            <p className="text-white/70 text-lg">
              Experience how Flow optimizes scheduling across timezones and preferences
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <CalendarDemo />
            <AISchedulingDemo />
          </div>
        </div>
      </section>

      {/* Job Types Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Flexible Job Scheduling
            </h2>
            <p className="text-white/70 text-lg">
              Handle any type of scheduled task with our powerful job scheduling engine
            </p>
          </motion.div>

          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {jobTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => setActiveTab(type.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === type.id
                    ? 'bg-primary text-white'
                    : 'bg-gray-800 text-white/70 hover:text-white'
                }`}
              >
                {type.name}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <GlassCard className="p-8 text-center">
              <h3 className="text-white font-semibold text-xl mb-4">
                {jobTypes.find(type => type.id === activeTab)?.name}
              </h3>
              <div className="flex flex-wrap justify-center gap-3">
                {jobTypes.find(type => type.id === activeTab)?.examples.map((example, index) => (
                  <span
                    key={index}
                    className="bg-primary/20 text-white px-3 py-2 rounded-lg text-sm"
                  >
                    {example}
                  </span>
                ))}
              </div>
            </GlassCard>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-dark-start to-dark-end">
        <div className="max-w-3xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Optimize Your Schedule?
            </h2>
            <p className="text-white/80 text-lg mb-8">
              Let AI handle your scheduling complexity and focus on what truly matters.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg" href="/get-started">
                Start Scheduling
              </Button>
              <Button variant="outline" size="lg" href="/calendar-integration">
                Connect Calendar
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}