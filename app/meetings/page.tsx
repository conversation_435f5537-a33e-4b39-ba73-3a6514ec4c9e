'use client';

import { motion } from 'framer-motion';
import { FeatureHero } from '@/components/sections/Hero';
import { FeatureCard } from '@/components/ui/GlassCard';
import GlassCard from '@/components/ui/GlassCard';
import Button from '@/components/common/Button';
import { staggerContainer, staggerItem } from '@/lib/animations';

const meetingFeatures = [
  {
    icon: '🎯',
    title: 'Real-time Transcription',
    description: 'High-accuracy speech-to-text with speaker identification and multilingual support.',
    benefits: ['95%+ accuracy', 'Speaker identification', 'Multi-language support'],
  },
  {
    icon: '📝',
    title: 'Smart Summaries',
    description: 'AI-generated summaries with key decisions, action items, and next steps.',
    benefits: ['Key decisions highlighted', 'Action items extracted', 'Next steps identified'],
  },
  {
    icon: '🤖',
    title: 'Auto-Join & Record',
    description: 'Automatically join scheduled meetings across all platforms.',
    benefits: ['Cross-platform support', 'Scheduled auto-join', 'Secure recording'],
  },
  {
    icon: '⚡',
    title: 'Workflow Integration',
    description: 'Turn meeting outcomes into tickets, tasks, and follow-ups.',
    benefits: ['Jira integration', 'Task creation', 'Automated follow-ups'],
  },
  {
    icon: '📊',
    title: 'Meeting Analytics',
    description: 'Insights into meeting effectiveness, participation rates, and productivity.',
    benefits: ['Effectiveness metrics', 'Participation tracking', 'Time analytics'],
  },
  {
    icon: '🔒',
    title: 'Enterprise Security',
    description: 'Bank-level encryption, compliance controls, and privacy features.',
    benefits: ['End-to-end encryption', 'Compliance ready', 'Privacy controls'],
  },
];

const integrations = [
  { name: 'Zoom', icon: '🔵' },
  { name: 'Teams', icon: '🟣' },
  { name: 'Google Meet', icon: '🟢' },
  { name: 'WebEx', icon: '🔴' },
  { name: 'Slack', icon: '🟡' },
  { name: 'Jira', icon: '🔷' },
];

// Mock meeting transcript component
function MeetingTranscriptDemo() {
  const transcript = [
    { speaker: 'Sarah Chen', avatar: '👩‍💼', time: '09:15', text: 'Let\'s review the Q4 roadmap priorities...' },
    { speaker: 'Mike Johnson', avatar: '👨‍💻', time: '09:16', text: 'The user authentication feature should be our top priority.' },
    { speaker: 'AI Assistant', avatar: '🤖', time: '09:17', text: '✨ Action detected: Create Jira epic for user authentication', action: true },
  ];

  return (
    <motion.div
      className="bg-gray-900/80 rounded-lg p-6 text-left font-mono text-sm"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-white/60 ml-2">Live Meeting Transcript</span>
        </div>
        <div className="text-green-400">● Recording</div>
      </div>
      
      <div className="space-y-3">
        {transcript.map((entry, index) => (
          <motion.div
            key={index}
            className="flex gap-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: index * 0.5 }}
          >
            <span className="text-2xl">{entry.avatar}</span>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-white font-semibold">{entry.speaker}</span>
                <span className="text-white/40 text-xs">{entry.time}</span>
              </div>
              <p className={entry.action ? 'text-green-400' : 'text-white/80'}>
                {entry.text}
              </p>
              {entry.action && (
                <motion.div
                  className="mt-2 flex gap-2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <button className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs text-white transition-colors">
                    📋 Create Jira Epic
                  </button>
                  <button className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-xs text-white transition-colors">
                    📅 Schedule Follow-up
                  </button>
                  <button className="bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-xs text-white transition-colors">
                    📧 Send Summary
                  </button>
                </motion.div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}

export default function MeetingsPage() {
  const stats = [
    { label: 'Meetings Processed', value: '10,000+' },
    { label: 'Languages Supported', value: '50+' },
    { label: 'Uptime', value: '99.9%' },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <FeatureHero
        icon="🎥"
        title="Smart Meetings"
        subtitle="Never miss a detail again. Flow automatically joins your meetings, captures everything, and turns conversations into actionable insights."
        stats={stats}
        gradient="primary"
      />

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Everything You Need for Smart Meetings
            </h2>
            <p className="text-white/70 text-lg max-w-2xl mx-auto">
              From real-time transcription to automated follow-ups, Flow handles every aspect of your meetings.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {meetingFeatures.map((feature, index) => (
              <motion.div key={index} variants={staggerItem}>
                <FeatureCard
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  benefits={feature.benefits}
                  gradient="primary"
                />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-5xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              See Flow in Action
            </h2>
            <p className="text-white/70 text-lg">
              Watch how Flow captures, processes, and turns your meetings into actionable insights
            </p>
          </motion.div>

          <MeetingTranscriptDemo />
        </div>
      </section>

      {/* Integrations Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Works with Your Favorite Tools
            </h2>
            <p className="text-white/70 text-lg">
              Seamlessly integrate with all major meeting platforms and productivity tools
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {integrations.map((integration, index) => (
              <motion.div
                key={index}
                variants={staggerItem}
              >
                <GlassCard className="text-center p-6 hover:scale-105 transition-transform">
                  <div className="text-3xl mb-2">{integration.icon}</div>
                  <div className="text-white text-sm font-medium">{integration.name}</div>
                </GlassCard>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-primary-start to-primary-end">
        <div className="max-w-3xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Transform Your Meetings?
            </h2>
            <p className="text-white/80 text-lg mb-8">
              Join thousands of teams using Flow to make their meetings more productive and actionable.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg" href="/get-started">
                Start Free Trial
              </Button>
              <Button variant="outline" size="lg" href="/demo">
                Schedule Demo
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}