'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { FeatureHero } from '@/components/sections/Hero';
import { FeatureCard } from '@/components/ui/GlassCard';
import GlassCard from '@/components/ui/GlassCard';
import Button from '@/components/common/Button';
import { staggerContainer, staggerItem } from '@/lib/animations';

const workflowFeatures = [
  {
    icon: '🎯',
    title: 'Visual Workflow Builder',
    description: 'Drag-and-drop interface to create complex workflows without coding.',
    benefits: ['No-code builder', 'Visual interface', 'Real-time testing'],
  },
  {
    icon: '🔗',
    title: 'Cross-Platform Integration',
    description: 'Connect all your tools and services with pre-built integrations.',
    benefits: ['100+ integrations', 'API connections', 'Custom webhooks'],
  },
  {
    icon: '🤖',
    title: 'Smart Triggers',
    description: 'AI-powered triggers that understand context and intent.',
    benefits: ['Natural language', 'Context awareness', 'Smart conditions'],
  },
  {
    icon: '⚡',
    title: 'Instant Execution',
    description: 'Real-time workflow execution with instant feedback and monitoring.',
    benefits: ['Real-time execution', 'Error handling', 'Performance monitoring'],
  },
];

const useCases = [
  {
    title: 'Incident Response',
    description: 'Automatic war rooms, alert engineers, track resolution',
    icon: '🚨',
    steps: ['Alert detected', 'Create war room', 'Notify team', 'Track progress'],
  },
  {
    title: 'Feature Releases',
    description: 'Coordinate launches with notifications and documentation',
    icon: '🚀',
    steps: ['Deploy ready', 'Update docs', 'Notify stakeholders', 'Monitor metrics'],
  },
  {
    title: 'Employee Onboarding',
    description: 'Automated account creation and training enrollment',
    icon: '👋',
    steps: ['New hire added', 'Create accounts', 'Send welcome', 'Schedule training'],
  },
  {
    title: 'Sales Automation',
    description: 'Lead scoring, follow-up sequences, CRM updates',
    icon: '💰',
    steps: ['Lead captured', 'Score quality', 'Assign rep', 'Schedule follow-up'],
  },
];

const templates = [
  { name: 'Email Digest', category: 'Communication', icon: '📧' },
  { name: 'Bug Triage', category: 'Development', icon: '🐛' },
  { name: 'Weekly Report', category: 'Analytics', icon: '📊' },
  { name: 'Backup & Sync', category: 'Operations', icon: '💾' },
  { name: 'Team Celebrations', category: 'HR', icon: '🎉' },
  { name: 'Lead Scoring', category: 'Sales', icon: '🎯' },
];

// Mock workflow builder component
function WorkflowBuilderDemo() {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  
  const nodes = [
    { id: 'trigger', x: 50, y: 100, type: 'trigger', label: 'Meeting Ended', icon: '🎥' },
    { id: 'ai', x: 200, y: 100, type: 'action', label: 'AI Analysis', icon: '🧠' },
    { id: 'condition', x: 350, y: 100, type: 'condition', label: 'Has Actions?', icon: '❓' },
    { id: 'create-task', x: 500, y: 50, type: 'action', label: 'Create Tasks', icon: '✅' },
    { id: 'send-summary', x: 500, y: 150, type: 'action', label: 'Send Summary', icon: '📧' },
  ];

  const connections = [
    { from: 'trigger', to: 'ai' },
    { from: 'ai', to: 'condition' },
    { from: 'condition', to: 'create-task' },
    { from: 'condition', to: 'send-summary' },
  ];

  return (
    <motion.div
      className="bg-gray-900/80 rounded-lg p-6 relative overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Toolbar */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h3 className="text-white font-semibold">Workflow Builder</h3>
          <div className="flex gap-2">
            <button className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-xs text-white transition-colors">
              ▶ Test Run
            </button>
            <button className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs text-white transition-colors">
              💾 Save & Deploy
            </button>
          </div>
        </div>
        <div className="text-green-400 text-sm">● Live Preview</div>
      </div>

      {/* Workflow Canvas */}
      <div className="relative h-64 bg-gray-800/50 rounded border border-gray-700">
        <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
          {/* Connection lines */}
          {connections.map((connection, index) => {
            const fromNode = nodes.find(n => n.id === connection.from);
            const toNode = nodes.find(n => n.id === connection.to);
            if (!fromNode || !toNode) return null;
            
            return (
              <motion.line
                key={index}
                x1={fromNode.x + 40}
                y1={fromNode.y + 15}
                x2={toNode.x}
                y2={toNode.y + 15}
                stroke="rgba(255, 255, 255, 0.3)"
                strokeWidth="2"
                strokeDasharray="5,5"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 1, delay: index * 0.2 }}
              />
            );
          })}
        </svg>

        {/* Workflow nodes */}
        {nodes.map((node, index) => (
          <motion.div
            key={node.id}
            className={`absolute w-20 h-8 rounded-lg flex items-center justify-center text-xs font-medium cursor-pointer transition-all ${
              node.type === 'trigger' ? 'bg-green-600' :
              node.type === 'condition' ? 'bg-yellow-600' :
              'bg-blue-600'
            } ${selectedNode === node.id ? 'ring-2 ring-white scale-110' : ''}`}
            style={{ left: node.x, top: node.y, zIndex: 2 }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            whileHover={{ scale: 1.05 }}
            onClick={() => setSelectedNode(selectedNode === node.id ? null : node.id)}
          >
            <span className="mr-1">{node.icon}</span>
            <span className="text-white text-xs">{node.label}</span>
          </motion.div>
        ))}
      </div>

      {/* Node details */}
      {selectedNode && (
        <motion.div
          className="mt-4 p-4 bg-gray-800 rounded border border-gray-600"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <h4 className="text-white font-medium mb-2">
            Node Configuration: {nodes.find(n => n.id === selectedNode)?.label}
          </h4>
          <div className="text-gray-300 text-sm">
            Configure triggers, conditions, and actions for this workflow step.
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

export default function WorkflowsPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <FeatureHero
        icon="⚡"
        title="Workflow Automation"
        subtitle="Turn natural language into powerful automations. Connect all your tools and let AI handle the repetitive tasks."
        gradient="secondary"
      />

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Powerful Workflow Automation
            </h2>
            <p className="text-white/70 text-lg max-w-2xl mx-auto">
              Create sophisticated workflows that connect all your tools and automate complex processes.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {workflowFeatures.map((feature, index) => (
              <motion.div key={index} variants={staggerItem}>
                <FeatureCard
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  benefits={feature.benefits}
                  gradient="secondary"
                />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Visual Workflow Builder
            </h2>
            <p className="text-white/70 text-lg">
              Build complex workflows with our intuitive drag-and-drop interface
            </p>
          </motion.div>

          <WorkflowBuilderDemo />
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Popular Use Cases
            </h2>
            <p className="text-white/70 text-lg">
              See how teams are using Flow to automate their most important processes
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {useCases.map((useCase, index) => (
              <motion.div key={index} variants={staggerItem}>
                <GlassCard className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="text-3xl">{useCase.icon}</div>
                    <div className="flex-1">
                      <h3 className="text-white font-semibold text-lg mb-2">
                        {useCase.title}
                      </h3>
                      <p className="text-white/70 mb-4">
                        {useCase.description}
                      </p>
                      <div className="space-y-2">
                        {useCase.steps.map((step, stepIndex) => (
                          <div key={stepIndex} className="flex items-center gap-2 text-sm text-white/60">
                            <div className="w-6 h-6 bg-secondary/30 rounded-full flex items-center justify-center text-xs">
                              {stepIndex + 1}
                            </div>
                            {step}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </GlassCard>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Templates Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready-to-Use Templates
            </h2>
            <p className="text-white/70 text-lg">
              Get started quickly with our library of pre-built workflow templates
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {templates.map((template, index) => (
              <motion.div key={index} variants={staggerItem}>
                <GlassCard className="p-6 text-center hover:scale-105 transition-transform cursor-pointer">
                  <div className="text-3xl mb-3">{template.icon}</div>
                  <h3 className="text-white font-semibold mb-2">{template.name}</h3>
                  <span className="inline-block px-3 py-1 bg-secondary/20 text-secondary text-xs rounded-full">
                    {template.category}
                  </span>
                </GlassCard>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-secondary-start to-secondary-end">
        <div className="max-w-3xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Automate Your Workflows?
            </h2>
            <p className="text-white/80 text-lg mb-8">
              Join thousands of teams using Flow to streamline their processes and boost productivity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg" href="/get-started">
                Start Building
              </Button>
              <Button variant="outline" size="lg" href="/templates">
                Browse Templates
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}