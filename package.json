{"name": "flow-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "next": "14.2.30", "react": "^18", "react-dom": "^18", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.30", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}