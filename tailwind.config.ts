import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: {
          start: "var(--primary-gradient-start)",
          end: "var(--primary-gradient-end)",
          DEFAULT: "#667eea",
        },
        secondary: {
          start: "var(--secondary-gradient-start)", 
          end: "var(--secondary-gradient-end)",
          DEFAULT: "#10b981",
        },
        dark: {
          start: "var(--dark-gradient-start)",
          end: "var(--dark-gradient-end)",
          DEFAULT: "#1e293b",
        },
        text: {
          primary: "var(--foreground)",
          secondary: "var(--text-secondary)",
        },
        glass: {
          bg: "var(--glass-bg)",
          border: "var(--glass-border)",
        },
      },
      fontFamily: {
        sans: ["-apple-system", "BlinkMacSystemFont", "Segoe UI", "Inter", "sans-serif"],
      },
      animation: {
        "float": "float 20s infinite linear",
        "logo-spin": "logoSpin 20s linear infinite",
        "logo-fast": "logoSpin 1s ease-in-out",
        "text-glow": "textGlow 3s ease-in-out infinite",
        "bounce-in": "bounceIn 0.6s ease-out",
        "fade-in": "fadeIn 0.8s ease-out",
        "slide-up": "slideUp 0.6s ease-out",
      },
      keyframes: {
        float: {
          "0%": {
            transform: "translateY(0px) rotate(0deg)",
            opacity: "0.1",
          },
          "50%": {
            transform: "translateY(-100px) rotate(180deg)",
            opacity: "0.2",
          },
          "100%": {
            transform: "translateY(0px) rotate(360deg)",
            opacity: "0.1",
          },
        },
        logoSpin: {
          "0%": { transform: "rotate(0deg)" },
          "100%": { transform: "rotate(360deg)" },
        },
        textGlow: {
          "0%, 100%": { backgroundPosition: "0% 50%" },
          "50%": { backgroundPosition: "100% 50%" },
        },
        bounceIn: {
          "0%": {
            transform: "scale(0.3) rotate(0deg)",
            opacity: "0",
          },
          "50%": {
            transform: "scale(1.05) rotate(180deg)",
          },
          "70%": {
            transform: "scale(0.9) rotate(270deg)",
          },
          "100%": {
            transform: "scale(1) rotate(360deg)",
            opacity: "1",
          },
        },
        fadeIn: {
          "0%": {
            opacity: "0",
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        slideUp: {
          "0%": {
            opacity: "0",
            transform: "translateY(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
      },
      backdropBlur: {
        'xs': '2px',
        'glass': '20px',
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'hover': '0 20px 50px rgba(0, 0, 0, 0.2)',
        'glow': '0 0 30px rgba(102, 126, 234, 0.3)',
      },
      spacing: {
        '18': '4.5rem',
        '22': '5.5rem',
      },
    },
  },
  plugins: [],
};
export default config;
