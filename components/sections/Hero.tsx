'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { heroText, staggerContainer, staggerItem } from '@/lib/animations';
import { FEATURE_CARDS } from '@/lib/constants';
import Button, { CTAButton } from '@/components/common/Button';
import { FeatureCard } from '@/components/ui/GlassCard';
import AIWorkflowVisualization from '@/components/animations/AIWorkflowVisualization';

interface HeroProps {
  title?: string;
  subtitle?: string;
  showFeatures?: boolean;
  className?: string;
}

export default function Hero({
  title = "Work Smarter with AI",
  subtitle = "Flow transforms how teams collaborate by intelligently connecting meetings, tasks, and knowledge across your entire workflow. Let AI handle the busy work while you focus on what matters.",
  showFeatures = true,
  className,
}: HeroProps) {
  return (
    <section
      className={cn(
        'relative min-h-screen flex items-center justify-center',
        'pt-20 pb-16 px-4 sm:px-6 lg:px-8',
        'text-center',
        className
      )}
    >
      {/* AI Workflow Background Visualization - Temporarily disabled for cleaner look */}
      {/* <AIWorkflowVisualization /> */}
      <div className="max-w-5xl mx-auto">
        {/* Hero Content */}
        <motion.div
          className="mb-16"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {/* Title */}
          <motion.h1
            className={cn(
              'text-4xl sm:text-5xl lg:text-6xl xl:text-7xl',
              'font-black leading-tight mb-6',
              'gradient-text'
            )}
            variants={heroText}
          >
            {title}
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            className={cn(
              'text-lg sm:text-xl lg:text-2xl',
              'text-white/80 max-w-3xl mx-auto',
              'leading-relaxed mb-12'
            )}
            variants={staggerItem}
          >
            {subtitle}
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
            variants={staggerItem}
          >
            <Button
              variant="filled"
              size="lg"
              href="/get-started"
              className="min-w-[192px]"
            >
              Start Free Trial
            </Button>
            
            <Button
              variant="ghost"
              size="lg"
              href="/demo"
              className="min-w-[192px]"
            >
              Watch Demo
            </Button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            className="flex flex-wrap justify-center items-center gap-6 text-white/60 text-sm"
            variants={staggerItem}
          >
            <div className="flex items-center gap-2">
              <span className="text-green-400">✓</span>
              Free 14-day trial
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-400">✓</span>
              No credit card required
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-400">✓</span>
              Cancel anytime
            </div>
          </motion.div>
        </motion.div>

        {/* Feature Cards Grid */}
        {showFeatures && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={staggerContainer}
            initial="initial"
            animate="animate"
          >
            {FEATURE_CARDS.map((feature, index) => (
              <motion.div
                key={feature.id}
                variants={staggerItem}
                transition={{ delay: 0.8 + index * 0.1 }}
              >
                <FeatureCard
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  benefits={feature.benefits}
                  href={feature.href}
                  gradient={feature.gradient as 'primary' | 'secondary' | 'dark'}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-white/60 rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>
    </section>
  );
}

// Specialized Hero variants
interface FeatureHeroProps {
  title: string;
  subtitle: string;
  icon: string;
  stats?: Array<{ label: string; value: string }>;
  gradient?: 'primary' | 'secondary' | 'dark';
}

export function FeatureHero({
  title,
  subtitle,
  icon,
  stats = [],
  gradient = 'primary',
}: FeatureHeroProps) {
  const gradientClasses = {
    primary: 'gradient-primary',
    secondary: 'gradient-secondary',
    dark: 'gradient-dark',
  };

  return (
    <section className={cn(
      'relative pt-24 pb-16 px-4 sm:px-6 lg:px-8',
      gradientClasses[gradient]
    )}>
      <div className="max-w-4xl mx-auto text-center">
        <motion.div
          className="text-6xl mb-6"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.8, type: "spring" }}
        >
          {icon}
        </motion.div>

        <motion.h1
          className="text-4xl sm:text-5xl lg:text-6xl font-black text-white mb-6"
          variants={heroText}
          initial="initial"
          animate="animate"
        >
          {title}
        </motion.h1>

        <motion.p
          className="text-xl text-white/80 max-w-2xl mx-auto mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {subtitle}
        </motion.p>

        {stats.length > 0 && (
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-white/70 text-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </motion.div>
        )}

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
        >
          <CTAButton href="/get-started">
            Get Started
          </CTAButton>
        </motion.div>
      </div>
    </section>
  );
}