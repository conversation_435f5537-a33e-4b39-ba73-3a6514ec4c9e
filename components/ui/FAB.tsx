'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { fabAnimation } from '@/lib/animations';

interface FABProps {
  icon: string;
  label: string;
  href?: string;
  onClick?: () => void;
  className?: string;
}

interface FABGroupProps {
  className?: string;
}

export function FAB({ icon, label, href, onClick, className }: FABProps) {
  const [isHovered, setIsHovered] = useState(false);

  const content = (
    <motion.div
      className={cn(
        'relative group',
        'w-12 h-12 rounded-full',
        'glass border border-white/20',
        'flex items-center justify-center',
        'text-white text-lg',
        'cursor-pointer',
        'shadow-lg hover:shadow-xl',
        'transition-all duration-300',
        className
      )}
      variants={fabAnimation}
      initial="initial"
      animate="animate"
      whileHover="hover"
      whileTap="tap"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      <span className="relative z-10">{icon}</span>
      
      {/* Tooltip */}
      <AnimatePresence>
        {isHovered && (
          <motion.div
            className="absolute right-14 top-1/2 transform -translate-y-1/2"
            initial={{ opacity: 0, x: 10, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 10, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <div className="glass px-3 py-2 rounded-lg border border-white/20 whitespace-nowrap">
              <span className="text-white text-sm font-medium">{label}</span>
              
              {/* Arrow */}
              <div className="absolute left-full top-1/2 transform -translate-y-1/2">
                <div className="w-0 h-0 border-l-4 border-l-white/20 border-t-4 border-t-transparent border-b-4 border-b-transparent" />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full bg-white/10"
        initial={{ scale: 0, opacity: 0 }}
        whileHover={{ scale: 1.2, opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );

  if (href) {
    return <a href={href}>{content}</a>;
  }

  return content;
}

export default function FABGroup({ className }: FABGroupProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const fabs = [
    {
      icon: '❓',
      label: 'Help & Support',
      href: '/help',
    },
    {
      icon: '💬',
      label: 'Contact Sales',
      onClick: () => {
        // Open chat or contact form
        console.log('Opening sales contact');
      },
    },
    {
      icon: '🎯',
      label: 'Schedule Demo',
      href: '/demo',
    },
  ];

  return (
    <div className={cn(
      'fixed bottom-6 right-6 z-50',
      'flex flex-col items-end gap-3',
      className
    )}>
      {/* Secondary FABs */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="flex flex-col gap-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {fabs.map((fab, index) => (
              <motion.div
                key={fab.label}
                initial={{ opacity: 0, y: 20, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 20, scale: 0.8 }}
                transition={{ 
                  duration: 0.2, 
                  delay: index * 0.05,
                  type: "spring",
                  stiffness: 400,
                  damping: 25
                }}
              >
                <FAB {...fab} />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <motion.div
        className={cn(
          'w-14 h-14 rounded-full',
          'glass border border-white/20',
          'flex items-center justify-center',
          'text-white text-xl',
          'cursor-pointer',
          'shadow-xl',
          'transition-all duration-300'
        )}
        variants={fabAnimation}
        initial="initial"
        animate="animate"
        whileHover="hover"
        whileTap="tap"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <motion.span
          animate={{ rotate: isExpanded ? 45 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {isExpanded ? '✕' : '💬'}
        </motion.span>
        
        {/* Pulse effect */}
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-white/30"
          animate={{
            scale: [1, 1.5],
            opacity: [0.7, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeOut",
          }}
        />
      </motion.div>
    </div>
  );
}