'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { glassCardAnimation } from '@/lib/animations';
import { ReactNode } from 'react';

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  href?: string;
  onClick?: () => void;
  hover?: boolean;
  gradient?: 'primary' | 'secondary' | 'dark' | 'none';
  size?: 'sm' | 'md' | 'lg';
}

const sizeClasses = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
};

const gradientClasses = {
  primary: 'bg-gradient-to-br from-primary-start/20 to-primary-end/20',
  secondary: 'bg-gradient-to-br from-secondary-start/20 to-secondary-end/20',
  dark: 'bg-gradient-to-br from-dark-start/20 to-dark-end/20',
  none: '',
};

export default function GlassCard({
  children,
  className,
  href,
  onClick,
  hover = true,
  gradient = 'none',
  size = 'md',
}: GlassCardProps) {
  const baseClasses = cn(
    'glass rounded-2xl',
    'border border-white/20',
    'backdrop-blur-glass',
    'shadow-glass',
    'transition-all duration-300',
    sizeClasses[size],
    gradientClasses[gradient],
    hover && 'hover-lift cursor-pointer',
    className
  );

  const content = (
    <motion.div
      className={baseClasses}
      variants={glassCardAnimation}
      initial="initial"
      animate="animate"
      whileHover={hover ? "hover" : undefined}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );

  if (href) {
    return (
      <a href={href} className="block">
        {content}
      </a>
    );
  }

  return content;
}

// Feature Card specialized component
interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
  benefits?: string[];
  href?: string;
  onClick?: () => void;
  gradient?: 'primary' | 'secondary' | 'dark';
  className?: string;
}

export function FeatureCard({
  icon,
  title,
  description,
  benefits = [],
  href,
  onClick,
  gradient = 'primary',
  className,
}: FeatureCardProps) {
  return (
    <GlassCard
      href={href}
      onClick={onClick}
      gradient={gradient}
      className={cn('text-left group', className)}
    >
      <div className="flex flex-col h-full">
        {/* Icon */}
        <motion.div
          className="text-4xl mb-4 transform transition-transform duration-300 group-hover:scale-110"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {icon}
        </motion.div>

        {/* Title */}
        <motion.h3
          className="text-xl font-bold text-white mb-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          {title}
        </motion.h3>

        {/* Description */}
        <motion.p
          className="text-white/80 text-sm leading-relaxed mb-4 flex-grow"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {description}
        </motion.p>

        {/* Benefits */}
        {benefits.length > 0 && (
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                className="flex items-center text-xs text-white/70"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
              >
                <div className="w-1.5 h-1.5 bg-white/60 rounded-full mr-2" />
                {benefit}
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Hover indicator */}
        <motion.div
          className="mt-4 flex items-center text-white/60 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ x: -10 }}
          animate={{ x: 0 }}
        >
          Learn more
          <motion.span
            className="ml-1"
            animate={{ x: [0, 4, 0] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            →
          </motion.span>
        </motion.div>
      </div>
    </GlassCard>
  );
}