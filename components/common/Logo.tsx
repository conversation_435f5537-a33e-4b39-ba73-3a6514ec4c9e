'use client';

import { motion } from 'framer-motion';
import { logoSpin, logoFastSpin } from '@/lib/animations';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
  onLogoClick?: () => void;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-10 h-10',
  lg: 'w-12 h-12',
};

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl',
};

export default function Logo({
  size = 'md',
  showText = true,
  className,
  onLogoClick,
}: LogoProps) {
  const [isSpinning, setIsSpinning] = useState(false);

  const handleLogoClick = () => {
    setIsSpinning(true);
    onLogoClick?.();
    
    // Reset after animation completes
    setTimeout(() => {
      setIsSpinning(false);
    }, 1000);
  };

  return (
    <div className={cn('flex items-center gap-3 cursor-pointer', className)}>
      <motion.div
        className={cn('relative', sizeClasses[size])}
        variants={isSpinning ? logoFastSpin : logoSpin}
        animate="animate"
        onClick={handleLogoClick}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <svg
          viewBox="0 0 40 40"
          fill="none"
          className="w-full h-full drop-shadow-lg"
        >
          {/* Outer circle */}
          <circle
            cx="20"
            cy="20"
            r="18"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            className="text-white"
          />
          
          {/* Checkmark */}
          <motion.path
            d="M12 20 L18 26 L28 14"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
          />
          
          {/* Inner glow circle */}
          <circle
            cx="20"
            cy="20"
            r="8"
            fill="rgba(255,255,255,0.1)"
            className="animate-pulse"
          />
          
          {/* Center dot */}
          <motion.circle
            cx="20"
            cy="20"
            r="3"
            fill="currentColor"
            className="text-white"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 1 }}
          />
          
          {/* Decorative orbiting dots */}
          <motion.circle
            cx="30"
            cy="20"
            r="1.5"
            fill="rgba(255,255,255,0.6)"
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "linear"
            }}
            style={{ transformOrigin: "20px 20px" }}
          />
          
          <motion.circle
            cx="10"
            cy="20"
            r="1"
            fill="rgba(255,255,255,0.4)"
            animate={{
              rotate: -360,
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "linear"
            }}
            style={{ transformOrigin: "20px 20px" }}
          />
        </svg>
      </motion.div>
      
      {showText && (
        <motion.div
          className={cn(
            'font-bold text-white',
            textSizeClasses[size]
          )}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <span className="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent font-extrabold tracking-tight">
            Flow
          </span>
        </motion.div>
      )}
    </div>
  );
}