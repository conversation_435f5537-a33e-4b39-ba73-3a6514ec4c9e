'use client';

import { motion } from 'framer-motion';
import { cn, createSparkleEffect } from '@/lib/utils';
import { buttonPress } from '@/lib/animations';
import { ReactNode, useRef } from 'react';

interface ButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'glass' | 'solid' | 'solid-dark' | 'gradient' | 'glow' | 'neon' | 'filled' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
  sparkle?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

const variantClasses = {
  primary: [
    'bg-white/95 text-primary border-2 border-transparent',
    'hover:bg-white hover:shadow-glow',
  ],
  secondary: [
    'bg-transparent text-white border-2 border-white/50',
    'hover:bg-white/10 hover:border-white',
  ],
  outline: [
    'bg-transparent text-white border-2 border-white/30',
    'hover:bg-white/5 hover:border-white/60',
  ],
  glass: [
    'glass text-white border border-white/20',
    'hover:bg-white/20',
  ],
  solid: [
    'bg-white text-gray-900 font-semibold shadow-lg',
    'hover:bg-gray-100 hover:shadow-xl hover:scale-105 transform',
  ],
  'solid-dark': [
    'bg-gray-900 text-white font-semibold shadow-lg',
    'hover:bg-gray-800 hover:shadow-xl hover:scale-105 transform',
  ],
  gradient: [
    'bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold shadow-2xl',
    'hover:from-blue-600 hover:to-purple-700 hover:shadow-3xl hover:-translate-y-1 transform',
  ],
  glow: [
    'bg-white text-gray-900 font-bold shadow-[0_0_20px_rgba(255,255,255,0.5)]',
    'hover:shadow-[0_0_30px_rgba(255,255,255,0.8)] hover:scale-105 transform',
  ],
  neon: [
    'bg-black text-cyan-400 font-bold border-2 border-cyan-400 shadow-[0_0_15px_rgba(0,255,255,0.5)]',
    'hover:bg-cyan-400 hover:text-black hover:shadow-[0_0_25px_rgba(0,255,255,0.8)] hover:border-cyan-300',
  ],
  filled: [
    'bg-white/95 text-primary border-2 border-transparent',
    'hover:bg-white hover:shadow-glow',
  ],
  ghost: [
    'bg-transparent text-white border-2 border-white/50',
    'hover:bg-white/10 hover:border-white',
  ],
};

const sizeClasses = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg',
};

export default function Button({
  children,
  variant = 'primary',
  size = 'md',
  href,
  onClick,
  disabled = false,
  sparkle = true,
  className,
  type = 'button',
}: ButtonProps) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const linkRef = useRef<HTMLAnchorElement>(null);

  const baseClasses = cn(
    'relative',
    'font-semibold rounded-full',
    'transition-all duration-300',
    'focus:outline-none focus:ring-2 focus:ring-white/50',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    variantClasses[variant],
    sizeClasses[size],
    className
  );

  const handleClick = () => {
    if (disabled) return;
    
    onClick?.();
    
    if (sparkle) {
      const element = buttonRef.current || linkRef.current;
      if (element) {
        createSparkleEffect(element);
      }
    }
  };

  const buttonContent = (
    <>
      {/* Content */}
      <span className="relative z-10 flex items-center justify-center gap-2">
        {children}
      </span>
    </>
  );

  if (href && !disabled) {
    return (
      <motion.a
        ref={linkRef}
        href={href}
        className={baseClasses}
        variants={buttonPress}
        initial="initial"
        whileHover="whileHover"
        whileTap="whileTap"
        onClick={handleClick}
      >
        {buttonContent}
      </motion.a>
    );
  }

  return (
    <motion.button
      ref={buttonRef}
      type={type}
      className={baseClasses}
      variants={buttonPress}
      initial="initial"
      whileHover={disabled ? undefined : "whileHover"}
      whileTap={disabled ? undefined : "whileTap"}
      onClick={handleClick}
      disabled={disabled}
    >
      {buttonContent}
    </motion.button>
  );
}

// Specialized CTA Button
interface CTAButtonProps extends Omit<ButtonProps, 'variant'> {
  gradient?: boolean;
}

export function CTAButton({ 
  gradient = true, 
  sparkle = true,
  className,
  ...props 
}: CTAButtonProps) {
  return (
    <Button
      variant="primary"
      size="lg"
      sparkle={sparkle}
      className={cn(
        gradient && 'bg-gradient-to-r from-white to-gray-50',
        'shadow-xl',
        className
      )}
      {...props}
    />
  );
}