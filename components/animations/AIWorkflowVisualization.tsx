'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface Agent {
  id: string;
  x: number;
  y: number;
  icon: string;
  label: string;
  color: string;
}

interface Payload {
  id: string;
  fromAgent: string;
  toAgent: string;
  progress: number;
  type: 'data' | 'task' | 'alert';
}

const agents: Agent[] = [
  { id: 'meeting', x: 15, y: 20, icon: '🎥', label: 'Meeting Agent', color: '#667eea' },
  { id: 'ai', x: 50, y: 15, icon: '🧠', label: 'AI Processor', color: '#10b981' },
  { id: 'workflow', x: 85, y: 25, icon: '⚡', label: 'Workflow Engine', color: '#f59e0b' },
  { id: 'calendar', x: 20, y: 60, icon: '📅', label: 'Calendar Agent', color: '#8b5cf6' },
  { id: 'email', x: 50, y: 70, icon: '📧', label: 'Email Agent', color: '#ef4444' },
  { id: 'knowledge', x: 80, y: 55, icon: '📚', label: 'Knowledge Base', color: '#06b6d4' },
];

const connections = [
  { from: 'meeting', to: 'ai' },
  { from: 'ai', to: 'workflow' },
  { from: 'workflow', to: 'calendar' },
  { from: 'workflow', to: 'email' },
  { from: 'ai', to: 'knowledge' },
  { from: 'calendar', to: 'email' },
];

export default function AIWorkflowVisualization() {
  const [payloads, setPayloads] = useState<Payload[]>([]);
  const [activeConnections, setActiveConnections] = useState<string[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      // Create new payload
      const connection = connections[Math.floor(Math.random() * connections.length)];
      const newPayload: Payload = {
        id: `payload-${Date.now()}`,
        fromAgent: connection.from,
        toAgent: connection.to,
        progress: 0,
        type: ['data', 'task', 'alert'][Math.floor(Math.random() * 3)] as 'data' | 'task' | 'alert',
      };

      setPayloads(prev => [...prev, newPayload]);
      setActiveConnections(prev => [...prev, `${connection.from}-${connection.to}`]);

      // Animate payload and remove after completion
      setTimeout(() => {
        setPayloads(prev => prev.filter(p => p.id !== newPayload.id));
        setActiveConnections(prev => prev.filter(c => c !== `${connection.from}-${connection.to}`));
      }, 2000);
    }, 1500);

    return () => clearInterval(interval);
  }, []);

  const getAgentPosition = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? { x: agent.x, y: agent.y } : { x: 0, y: 0 };
  };

  const getConnectionPath = (fromId: string, toId: string) => {
    const from = getAgentPosition(fromId);
    const to = getAgentPosition(toId);
    
    // Create curved path
    const midX = (from.x + to.x) / 2;
    const midY = (from.y + to.y) / 2 - 10; // Curve upward
    
    return `M ${from.x} ${from.y} Q ${midX} ${midY} ${to.x} ${to.y}`;
  };

  return (
    <div className="absolute inset-0 pointer-events-none opacity-10">
      <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid slice">
        {/* Connection lines */}
        {connections.map((connection, index) => {
          const pathId = `${connection.from}-${connection.to}`;
          const isActive = activeConnections.includes(pathId);
          
          return (
            <g key={pathId}>
              {/* Base path */}
              <motion.path
                d={getConnectionPath(connection.from, connection.to)}
                stroke="rgba(255, 255, 255, 0.1)"
                strokeWidth="0.3"
                fill="none"
                strokeDasharray="0.5 0.5"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 2, delay: index * 0.2 }}
              />
              
              {/* Active connection glow */}
              {isActive && (
                <motion.path
                  d={getConnectionPath(connection.from, connection.to)}
                  stroke="rgba(255, 255, 255, 0.6)"
                  strokeWidth="0.5"
                  fill="none"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                />
              )}
            </g>
          );
        })}

        {/* Agents */}
        {agents.map((agent, index) => (
          <motion.g
            key={agent.id}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            {/* Agent glow */}
            <motion.circle
              cx={agent.x}
              cy={agent.y}
              r="2"
              fill={agent.color}
              opacity={0.1}
              animate={{
                r: [1.5, 2.5, 1.5],
                opacity: [0.05, 0.15, 0.05],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            
            {/* Agent circle */}
            <circle
              cx={agent.x}
              cy={agent.y}
              r="2"
              fill="rgba(255, 255, 255, 0.9)"
              stroke={agent.color}
              strokeWidth="0.2"
            />
            
            {/* Agent icon (simplified representation) */}
            <circle
              cx={agent.x}
              cy={agent.y}
              r="1"
              fill={agent.color}
              opacity={0.8}
            />
          </motion.g>
        ))}

        {/* Animated payloads */}
        {payloads.map((payload) => {
          const fromPos = getAgentPosition(payload.fromAgent);
          const toPos = getAgentPosition(payload.toAgent);
          
          return (
            <motion.circle
              key={payload.id}
              r="0.5"
              fill="rgba(255, 255, 255, 0.8)"
              initial={{ cx: fromPos.x, cy: fromPos.y, opacity: 0 }}
              animate={{
                cx: toPos.x,
                cy: toPos.y,
                opacity: [0, 1, 1, 0],
              }}
              transition={{
                duration: 2,
                ease: "easeInOut",
              }}
            />
          );
        })}

        {/* Data flow particles */}
        {[...Array(8)].map((_, i) => (
          <motion.circle
            key={`particle-${i}`}
            r="0.1"
            fill="rgba(255, 255, 255, 0.2)"
            initial={{
              cx: Math.random() * 100,
              cy: Math.random() * 100,
              opacity: 0,
            }}
            animate={{
              cx: Math.random() * 100,
              cy: Math.random() * 100,
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: Math.random() * 8 + 5,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut",
            }}
          />
        ))}
      </svg>

      {/* Floating info cards */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 2 }}
        >
          <motion.div
            className="text-white/60 text-sm mb-2"
            animate={{ opacity: [0.4, 0.8, 0.4] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            AI Agents Processing Workflows
          </motion.div>
          <motion.div
            className="flex justify-center gap-4 text-xs text-white/40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 3 }}
          >
            <span>🎥 Meetings</span>
            <span>⚡ Automation</span>
            <span>📅 Scheduling</span>
            <span>🧠 AI Processing</span>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}