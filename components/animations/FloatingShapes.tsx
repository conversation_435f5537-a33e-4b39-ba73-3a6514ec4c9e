'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FloatingShape {
  id: number;
  size: number;
  top: string;
  left: string;
  delay: number;
  duration: number;
  color?: string;
}

interface FloatingShapesProps {
  count?: number;
  className?: string;
  opacity?: number;
}

const generateShapes = (count: number): FloatingShape[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    size: Math.random() * 120 + 40, // 40px to 160px
    top: `${Math.random() * 100}%`,
    left: `${Math.random() * 100}%`,
    delay: Math.random() * 10, // 0 to 10 seconds delay
    duration: Math.random() * 10 + 15, // 15 to 25 seconds duration
    color: `rgba(255, 255, 255, ${Math.random() * 0.1 + 0.05})`, // 0.05 to 0.15 opacity
  }));
};

const floatingAnimation = {
  animate: {
    y: [-20, -100, -20],
    x: [-10, 10, -10],
    rotate: [0, 180, 360],
    scale: [1, 1.1, 1],
    opacity: [0.05, 0.15, 0.05],
  },
};

export default function FloatingShapes({
  count = 0,
  className,
  opacity = 0.02,
}: FloatingShapesProps) {
  const shapes = generateShapes(count);

  return (
    <div className={cn('fixed inset-0 pointer-events-none z-0', className)}>
      {/* Removed floating shapes to clean up background */}
      
      
    </div>
  );
}