export const NAVIGATION_LINKS = [
  { href: '/features', label: 'Features' },
  { href: '/integrations', label: 'Integrations' },
  { href: '/pricing', label: 'Pricing' },
  { href: '/about', label: 'About' },
] as const;

export const FEATURE_CARDS = [
  {
    id: 'meetings',
    icon: '🎥',
    title: 'Smart Meetings',
    description: 'Automatically join, transcribe, and extract actionable insights from every meeting across platforms.',
    href: '/meetings',
    benefits: ['95%+ accuracy', 'Speaker identification', 'Action extraction'],
    gradient: 'gradient-primary',
  },
  {
    id: 'workflows',
    icon: '⚡',
    title: 'Workflow Automation',
    description: 'Turn conversations into actions with intelligent workflows that connect all your tools.',
    href: '/workflows',
    benefits: ['Cross-platform integration', 'Natural language triggers', 'Auto-follow-up'],
    gradient: 'gradient-secondary',
  },
  {
    id: 'schedules',
    icon: '📅',
    title: 'Smart Scheduling',
    description: 'AI-powered scheduling that learns your preferences and optimizes your calendar automatically.',
    href: '/schedules',
    benefits: ['Timezone optimization', 'Preference learning', 'Auto-conflict resolution'],
    gradient: 'gradient-dark',
  },
  {
    id: 'knowledge',
    icon: '🧠',
    title: 'Knowledge Hub',
    description: 'Universal search across all your tools with AI-powered insights and recommendations.',
    href: '/knowledge',
    benefits: ['Universal search', 'AI insights', 'Smart recommendations'],
    gradient: 'gradient-primary',
  },
  {
    id: 'agents',
    icon: '🤖',
    title: 'AI Agents',
    description: 'Deploy specialized AI agents for email, tasks, research, and more - your 24/7 digital workforce.',
    href: '/agents',
    benefits: ['24/7 availability', 'Task specialization', 'Multi-platform support'],
    gradient: 'gradient-secondary',
  },
  {
    id: 'analytics',
    icon: '📊',
    title: 'Analytics & Insights',
    description: 'Get deep insights into team productivity, meeting effectiveness, and workflow optimization.',
    href: '/analytics',
    benefits: ['Team productivity metrics', 'Meeting effectiveness', 'Workflow optimization'],
    gradient: 'gradient-dark',
  },
] as const;

export const INTEGRATIONS = [
  { name: 'Slack', logo: '/icons/slack.svg', category: 'Communication' },
  { name: 'Microsoft Teams', logo: '/icons/teams.svg', category: 'Communication' },
  { name: 'Zoom', logo: '/icons/zoom.svg', category: 'Meetings' },
  { name: 'Google Meet', logo: '/icons/meet.svg', category: 'Meetings' },
  { name: 'Jira', logo: '/icons/jira.svg', category: 'Project Management' },
  { name: 'GitHub', logo: '/icons/github.svg', category: 'Development' },
  { name: 'Notion', logo: '/icons/notion.svg', category: 'Documentation' },
  { name: 'Salesforce', logo: '/icons/salesforce.svg', category: 'CRM' },
] as const;

export const API_ENDPOINTS = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'https://api.flow.example.com',
  APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'https://flow.example.com',
} as const;

export const ANIMATION_DELAYS = {
  STAGGER_CHILDREN: 0.1,
  CARD_HOVER: 0.3,
  PAGE_TRANSITION: 0.2,
} as const;